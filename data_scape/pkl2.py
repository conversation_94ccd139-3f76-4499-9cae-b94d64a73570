import pickle
import os
import sys

def read_pkl(pkl_path):
    """
    Read data from a pickle file
    
    Parameters:
        pkl_path (str): Path to the .pkl file
    
    Returns:
        object: Data loaded from the pickle file
    """
    try:
        with open(pkl_path, 'rb') as pkl_file:
            data = pickle.load(pkl_file)
        return data
    except FileNotFoundError:
        print(f"Error: File not found at {pkl_path}")
    except pickle.PickleError as e:
        print(f"Error: Invalid pickle data - {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python pkl_reader.py <path_to_pkl_file>")
        return
    
    pkl_path = sys.argv[1]
    if not os.path.exists(pkl_path):
        print(f"Error: File does not exist at {pkl_path}")
        return
    
    data = read_pkl(pkl_path)
    if data is not None:
        print(f"Successfully loaded data of type: {type(data)}")
        # Add custom processing here based on expected data type
        print(f"Data preview: {data}")

if __name__ == "__main__":
    main()


    # python pkl2.py /home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/2025-06-09T16_50_53.997363.pkl