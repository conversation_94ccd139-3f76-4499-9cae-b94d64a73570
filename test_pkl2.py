#!/usr/bin/env python3
"""
Test script for pkl2.py functionality
"""

import os
import sys
import subprocess

def test_pkl2_functionality():
    """Test the pkl2.py script functionality"""
    
    print("Testing pkl2.py functionality...")
    
    # Test 1: Show help message
    print("\n1. Testing help message:")
    result = subprocess.run([sys.executable, "data_scape/pkl2.py"], 
                          capture_output=True, text=True)
    print("STDOUT:", result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    # Test 2: Test with example pkl file path (if it exists)
    example_pkl_path = "/home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/2025-06-09T16_50_53.997363.pkl"
    
    if os.path.exists(example_pkl_path):
        print(f"\n2. Testing single pkl file: {example_pkl_path}")
        result = subprocess.run([sys.executable, "data_scape/pkl2.py", example_pkl_path], 
                              capture_output=True, text=True)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
    else:
        print(f"\n2. Example pkl file not found: {example_pkl_path}")
    
    # Test 3: Test with example directory (if it exists)
    example_dir = "/home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/"
    
    if os.path.exists(example_dir):
        print(f"\n3. Testing directory processing: {example_dir}")
        result = subprocess.run([sys.executable, "data_scape/pkl2.py", example_dir, "test_output.mp4", "10", "2", "2"], 
                              capture_output=True, text=True)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        # Check if video was created
        if os.path.exists("test_output.mp4"):
            print("✓ Video file created successfully!")
            file_size = os.path.getsize("test_output.mp4")
            print(f"  Video file size: {file_size} bytes")
        else:
            print("✗ Video file was not created")
    else:
        print(f"\n3. Example directory not found: {example_dir}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_pkl2_functionality()
