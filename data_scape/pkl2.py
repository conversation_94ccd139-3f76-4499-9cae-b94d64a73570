import pickle
import os
import sys
import cv2
import numpy as np
import datetime
import glob

def read_pkl(pkl_path):
    """
    Read data from a pickle file

    Parameters:
        pkl_path (str): Path to the .pkl file

    Returns:
        object: Data loaded from the pickle file
    """
    try:
        with open(pkl_path, 'rb') as pkl_file:
            data = pickle.load(pkl_file)
        return data
    except FileNotFoundError:
        print(f"Error: File not found at {pkl_path}")
        return None
    except pickle.PickleError as e:
        print(f"Error: Invalid pickle data - {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None


def decode_image(image_data):
    """
    Decode image data from various formats

    Parameters:
        image_data: Image data in various formats (encoded bytes, numpy array, etc.)

    Returns:
        numpy.ndarray: Decoded image as numpy array
    """
    if image_data is None:
        return None

    # If it's already a numpy array with proper shape, return as is
    if isinstance(image_data, np.ndarray) and len(image_data.shape) == 3:
        return image_data

    # Try to decode as compressed image (JPEG, PNG, etc.)
    if isinstance(image_data, (bytes, np.ndarray)) and len(image_data.shape) <= 2:
        try:
            decoded = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            if decoded is not None:
                return decoded
        except:
            pass

    # Try to reshape raw bytes to common image dimensions
    if isinstance(image_data, (bytes, np.ndarray)):
        if isinstance(image_data, bytes):
            data = np.frombuffer(image_data, dtype=np.uint8)
        else:
            data = image_data.flatten()

        # Common image sizes
        if data.size == 2764800:  # 720x1280x3
            return data.reshape(720, 1280, 3)
        elif data.size == 921600:  # 480x640x3
            return data.reshape(480, 640, 3)
        elif data.size == 1228800:  # 480x640x4 (RGBA)
            return data.reshape(480, 640, 4)[:, :, :3]  # Convert RGBA to RGB

    return None


def extract_images_from_pkl(pkl_path):
    """
    Extract all images from a pkl file

    Parameters:
        pkl_path (str): Path to the pkl file

    Returns:
        dict: Dictionary of camera_name -> image_array
    """
    data = read_pkl(pkl_path)
    if data is None:
        return {}

    images = {}

    # Try to find images in common data structures
    if isinstance(data, dict):
        # Look for 'images' key
        if 'images' in data:
            for cam_name, img_data in data['images'].items():
                decoded_img = decode_image(img_data)
                if decoded_img is not None:
                    images[cam_name] = decoded_img

        # Look for direct image data in top level
        for key, value in data.items():
            if 'image' in key.lower() or 'camera' in key.lower():
                decoded_img = decode_image(value)
                if decoded_img is not None:
                    images[key] = decoded_img

    return images


def parse_timestamp_from_filename(filename):
    """
    Parse timestamp from filename like '2025-06-09T16_51_05.803324.pkl'

    Parameters:
        filename (str): Filename containing timestamp

    Returns:
        datetime.datetime: Parsed timestamp or None if parsing fails
    """
    try:
        # Remove .pkl extension
        timestamp_str = filename.replace('.pkl', '')

        # Handle both formats: with colons and with underscores
        if ':' in timestamp_str:
            format_str = '%Y-%m-%dT%H:%M:%S.%f'
        else:
            format_str = '%Y-%m-%dT%H_%M_%S.%f'

        return datetime.datetime.strptime(timestamp_str, format_str)
    except ValueError as e:
        print(f"Error parsing timestamp from {filename}: {e}")
        return None


def get_sorted_pkl_files(directory):
    """
    Get all pkl files in directory sorted by timestamp

    Parameters:
        directory (str): Directory path containing pkl files

    Returns:
        list: List of pkl file paths sorted by timestamp
    """
    pkl_files = []

    # Find all pkl files
    for pkl_file in glob.glob(os.path.join(directory, "*.pkl")):
        filename = os.path.basename(pkl_file)
        timestamp = parse_timestamp_from_filename(filename)
        if timestamp is not None:
            pkl_files.append((timestamp, pkl_file))

    # Sort by timestamp
    pkl_files.sort(key=lambda x: x[0])

    return [pkl_path for _, pkl_path in pkl_files]


def create_image_grid(images_dict, grid_size=(2, 2), image_size=(640, 480)):
    """
    Create a grid layout from multiple camera images

    Parameters:
        images_dict (dict): Dictionary of camera_name -> image_array
        grid_size (tuple): Grid size as (cols, rows)
        image_size (tuple): Size to resize each image to (width, height)

    Returns:
        numpy.ndarray: Combined grid image
    """
    cols, rows = grid_size
    max_images = cols * rows

    # Resize all images and add titles
    processed_images = []
    for i, (camera_name, img) in enumerate(images_dict.items()):
        if i >= max_images:
            break

        # Resize image
        resized_img = cv2.resize(img, image_size)

        # Add title
        title_height = 30
        title_area = np.ones((title_height, image_size[0], 3), dtype=np.uint8) * 255
        cv2.putText(title_area, camera_name, (5, 20), cv2.FONT_HERSHEY_SIMPLEX,
                   0.5, (0, 0, 0), 1, cv2.LINE_AA)

        # Combine title and image
        combined = cv2.vconcat([title_area, resized_img])
        processed_images.append(combined)

    # Fill empty slots with blank images
    blank_img = np.ones((image_size[1] + 30, image_size[0], 3), dtype=np.uint8) * 255
    while len(processed_images) < max_images:
        processed_images.append(blank_img.copy())

    # Create grid
    grid_rows = []
    for row in range(rows):
        row_images = processed_images[row * cols:(row + 1) * cols]
        grid_rows.append(cv2.hconcat(row_images))

    return cv2.vconcat(grid_rows)


def create_videos_by_camera(input_dir, output_dir=".", fps=10, image_size=(640, 480)):
    """
    Create separate MP4 videos for each camera view from pkl files in a directory

    Parameters:
        input_dir (str): Directory containing pkl files
        output_dir (str): Directory to save output videos
        fps (int): Frames per second for the video
        image_size (tuple): Size for each camera image (width, height)

    Returns:
        dict: Dictionary of camera_name -> success_status
    """
    print(f"Scanning directory: {input_dir}")
    pkl_files = get_sorted_pkl_files(input_dir)

    if not pkl_files:
        print("No pkl files found in directory")
        return {}

    print(f"Found {len(pkl_files)} pkl files")

    # Dictionary to store video writers for each camera
    video_writers = {}
    frame_counts = {}
    camera_names = set()

    # First pass: collect all camera names
    print("Detecting camera views...")
    for pkl_path in pkl_files[:10]:  # Check first 10 files to get camera names
        images = extract_images_from_pkl(pkl_path)
        if images:
            camera_names.update(images.keys())

    print(f"Found camera views: {list(camera_names)}")

    # Initialize frame counters
    for camera in camera_names:
        frame_counts[camera] = 0

    try:
        for i, pkl_path in enumerate(pkl_files):
            print(f"Processing {i+1}/{len(pkl_files)}: {os.path.basename(pkl_path)}")

            # Extract images from pkl
            images = extract_images_from_pkl(pkl_path)

            if not images:
                print(f"  No images found in {pkl_path}")
                continue

            # Process each camera view
            for camera_name, image in images.items():
                if camera_name not in camera_names:
                    continue

                # Resize image
                resized_image = cv2.resize(image, image_size)

                # Initialize video writer for this camera if not exists
                if camera_name not in video_writers:
                    output_video_path = os.path.join(output_dir, f"{camera_name}.mp4")
                    height, width = resized_image.shape[:2]
                    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                    video_writers[camera_name] = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
                    print(f"  Initialized video writer for {camera_name}: {width}x{height} at {fps} fps")

                # Write frame to video
                video_writers[camera_name].write(resized_image)
                frame_counts[camera_name] += 1

        # Report results
        results = {}
        for camera_name in camera_names:
            if camera_name in frame_counts and frame_counts[camera_name] > 0:
                print(f"Successfully created video for {camera_name} with {frame_counts[camera_name]} frames")
                results[camera_name] = True
            else:
                print(f"No frames found for camera {camera_name}")
                results[camera_name] = False

        return results

    except Exception as e:
        print(f"Error creating videos: {e}")
        return {camera: False for camera in camera_names}

    finally:
        # Release all video writers
        for camera_name, writer in video_writers.items():
            if writer is not None:
                writer.release()
                output_path = os.path.join(output_dir, f"{camera_name}.mp4")
                print(f"Video saved to: {output_path}")


def create_video_from_pkl_directory(input_dir, output_video_path, fps=10, grid_size=(2, 2), image_size=(640, 480)):
    """
    Create MP4 video from pkl files in a directory (legacy function for grid layout)

    Parameters:
        input_dir (str): Directory containing pkl files
        output_video_path (str): Output video file path
        fps (int): Frames per second for the video
        grid_size (tuple): Grid layout for multiple cameras (cols, rows)
        image_size (tuple): Size for each camera image (width, height)

    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Scanning directory: {input_dir}")
    pkl_files = get_sorted_pkl_files(input_dir)

    if not pkl_files:
        print("No pkl files found in directory")
        return False

    print(f"Found {len(pkl_files)} pkl files")

    # Initialize video writer
    video_writer = None
    frame_count = 0

    try:
        for i, pkl_path in enumerate(pkl_files):
            print(f"Processing {i+1}/{len(pkl_files)}: {os.path.basename(pkl_path)}")

            # Extract images from pkl
            images = extract_images_from_pkl(pkl_path)

            if not images:
                print(f"  No images found in {pkl_path}")
                continue

            # Create grid image
            if len(images) == 1:
                # Single camera - use the image directly
                frame = list(images.values())[0]
                frame = cv2.resize(frame, (image_size[0] * grid_size[0], image_size[1] * grid_size[1]))
            else:
                # Multiple cameras - create grid
                frame = create_image_grid(images, grid_size, image_size)

            # Initialize video writer with first frame
            if video_writer is None:
                height, width = frame.shape[:2]
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                video_writer = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
                print(f"Initialized video writer: {width}x{height} at {fps} fps")

            # Write frame to video
            video_writer.write(frame)
            frame_count += 1

        print(f"Successfully created video with {frame_count} frames")
        return True

    except Exception as e:
        print(f"Error creating video: {e}")
        return False

    finally:
        if video_writer is not None:
            video_writer.release()
            print(f"Video saved to: {output_video_path}")

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single file: python pkl2.py <path_to_pkl_file>")
        print("  Create separate videos: python pkl2.py <directory_path> [output_dir] [fps]")
        print("  Create grid video: python pkl2.py <directory_path> --grid [output_video.mp4] [fps] [grid_cols] [grid_rows]")
        print("Examples:")
        print("  python pkl2.py /path/to/file.pkl")
        print("  python pkl2.py /path/to/pkl_directory")
        print("  python pkl2.py /path/to/pkl_directory ./videos 15")
        print("  python pkl2.py /path/to/pkl_directory --grid output.mp4 15 2 2")
        return

    input_path = sys.argv[1]

    if not os.path.exists(input_path):
        print(f"Error: Path does not exist: {input_path}")
        return

    # Check if input is a file or directory
    if os.path.isfile(input_path):
        # Single file mode - show pkl content and extract images
        print(f"Processing single pkl file: {input_path}")

        data = read_pkl(input_path)
        if data is not None:
            print(f"Successfully loaded data of type: {type(data)}")

            # Extract and show images
            images = extract_images_from_pkl(input_path)
            if images:
                print(f"Found {len(images)} images:")
                for cam_name, img in images.items():
                    print(f"  {cam_name}: {img.shape}")

                # Save first image as preview
                if images:
                    first_cam = list(images.keys())[0]
                    preview_path = input_path.replace('.pkl', '_preview.jpg')
                    cv2.imwrite(preview_path, images[first_cam])
                    print(f"Saved preview image: {preview_path}")
            else:
                print("No images found in pkl file")
                print(f"Data preview: {str(data)[:200]}...")

    elif os.path.isdir(input_path):
        # Directory mode - check if grid mode is requested
        if len(sys.argv) > 2 and sys.argv[2] == "--grid":
            # Grid video mode (legacy)
            print(f"Processing directory for grid video: {input_path}")

            output_video = sys.argv[3] if len(sys.argv) > 3 else "output_video.mp4"
            fps = int(sys.argv[4]) if len(sys.argv) > 4 else 10
            grid_cols = int(sys.argv[5]) if len(sys.argv) > 5 else 2
            grid_rows = int(sys.argv[6]) if len(sys.argv) > 6 else 2

            print(f"Output video: {output_video}")
            print(f"FPS: {fps}")
            print(f"Grid size: {grid_cols}x{grid_rows}")

            # Create grid video
            success = create_video_from_pkl_directory(
                input_path,
                output_video,
                fps=fps,
                grid_size=(grid_cols, grid_rows)
            )

            if success:
                print("Grid video creation completed successfully!")
            else:
                print("Grid video creation failed!")

        else:
            # Separate videos mode (new default)
            print(f"Processing directory for separate camera videos: {input_path}")

            output_dir = sys.argv[2] if len(sys.argv) > 2 else "."
            fps = int(sys.argv[3]) if len(sys.argv) > 3 else 10

            # Create output directory if it doesn't exist
            if output_dir != "." and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"Created output directory: {output_dir}")

            print(f"Output directory: {output_dir}")
            print(f"FPS: {fps}")

            # Create separate videos for each camera
            results = create_videos_by_camera(
                input_path,
                output_dir,
                fps=fps
            )

            # Report results
            successful_cameras = [cam for cam, success in results.items() if success]
            failed_cameras = [cam for cam, success in results.items() if not success]

            if successful_cameras:
                print(f"\n✓ Successfully created videos for: {', '.join(successful_cameras)}")
            if failed_cameras:
                print(f"\n✗ Failed to create videos for: {', '.join(failed_cameras)}")

            if successful_cameras:
                print("Separate camera videos creation completed successfully!")
            else:
                print("Video creation failed for all cameras!")


if __name__ == "__main__":
    main()


# Examples:
# Single file: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/2025-06-09T16_50_53.997363.pkl
# Separate videos: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/ ./videos 15
# Grid video: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/ --grid output.mp4 15 2 2