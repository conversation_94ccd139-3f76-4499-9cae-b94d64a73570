import pickle
import os
import sys
import cv2
import numpy as np
import datetime
import glob
import matplotlib.pyplot as plt
import matplotlib.cm as cm
from matplotlib.colors import Normalize

def read_pkl(pkl_path):
    """
    Read data from a pickle file

    Parameters:
        pkl_path (str): Path to the .pkl file

    Returns:
        object: Data loaded from the pickle file
    """
    try:
        with open(pkl_path, 'rb') as pkl_file:
            data = pickle.load(pkl_file)
        return data
    except FileNotFoundError:
        print(f"Error: File not found at {pkl_path}")
        return None
    except pickle.PickleError as e:
        print(f"Error: Invalid pickle data - {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None


def decode_image(image_data):
    """
    Decode image data from various formats

    Parameters:
        image_data: Image data in various formats (encoded bytes, numpy array, etc.)

    Returns:
        numpy.ndarray: Decoded image as numpy array
    """
    if image_data is None:
        return None

    # If it's already a numpy array with proper shape, return as is
    if isinstance(image_data, np.ndarray) and len(image_data.shape) == 3:
        return image_data

    # Try to decode as compressed image (JPEG, PNG, etc.)
    if isinstance(image_data, (bytes, np.ndarray)) and len(image_data.shape) <= 2:
        try:
            decoded = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            if decoded is not None:
                return decoded
        except:
            pass

    # Try to reshape raw bytes to common image dimensions
    if isinstance(image_data, (bytes, np.ndarray)):
        if isinstance(image_data, bytes):
            data = np.frombuffer(image_data, dtype=np.uint8)
        else:
            data = image_data.flatten()

        # Common image sizes
        if data.size == 2764800:  # 720x1280x3
            return data.reshape(720, 1280, 3)
        elif data.size == 921600:  # 480x640x3
            return data.reshape(480, 640, 3)
        elif data.size == 1228800:  # 480x640x4 (RGBA)
            return data.reshape(480, 640, 4)[:, :, :3]  # Convert RGBA to RGB

    return None


def extract_images_from_pkl(pkl_path):
    """
    Extract all images from a pkl file

    Parameters:
        pkl_path (str): Path to the pkl file

    Returns:
        dict: Dictionary of camera_name -> image_array
    """
    data = read_pkl(pkl_path)
    if data is None:
        return {}

    images = {}

    # Try to find images in common data structures
    if isinstance(data, dict):
        # Look for 'images' key
        if 'images' in data:
            for cam_name, img_data in data['images'].items():
                decoded_img = decode_image(img_data)
                if decoded_img is not None:
                    images[cam_name] = decoded_img

        # Look for direct image data in top level
        for key, value in data.items():
            if 'image' in key.lower() or 'camera' in key.lower():
                decoded_img = decode_image(value)
                if decoded_img is not None:
                    images[key] = decoded_img

    return images


def parse_timestamp_from_filename(filename):
    """
    Parse timestamp from filename like '2025-06-09T16_51_05.803324.pkl'

    Parameters:
        filename (str): Filename containing timestamp

    Returns:
        datetime.datetime: Parsed timestamp or None if parsing fails
    """
    try:
        # Remove .pkl extension
        timestamp_str = filename.replace('.pkl', '')

        # Handle both formats: with colons and with underscores
        if ':' in timestamp_str:
            format_str = '%Y-%m-%dT%H:%M:%S.%f'
        else:
            format_str = '%Y-%m-%dT%H_%M_%S.%f'

        return datetime.datetime.strptime(timestamp_str, format_str)
    except ValueError as e:
        print(f"Error parsing timestamp from {filename}: {e}")
        return None


def get_sorted_pkl_files(directory):
    """
    Get all pkl files in directory sorted by timestamp

    Parameters:
        directory (str): Directory path containing pkl files

    Returns:
        list: List of pkl file paths sorted by timestamp
    """
    pkl_files = []

    # Find all pkl files
    for pkl_file in glob.glob(os.path.join(directory, "*.pkl")):
        filename = os.path.basename(pkl_file)
        timestamp = parse_timestamp_from_filename(filename)
        if timestamp is not None:
            pkl_files.append((timestamp, pkl_file))

    # Sort by timestamp
    pkl_files.sort(key=lambda x: x[0])

    return [pkl_path for _, pkl_path in pkl_files]


def calculate_fps_from_timestamps(pkl_files):
    """
    Calculate FPS based on timestamps from pkl filenames or file contents

    Parameters:
        pkl_files (list): List of pkl file paths sorted by timestamp

    Returns:
        float: Calculated FPS, or None if calculation fails
    """
    if len(pkl_files) < 2:
        print("Need at least 2 files to calculate FPS")
        return None

    timestamps = []

    # Method 1: Try to extract timestamps from filenames
    print("Calculating FPS from filename timestamps...")
    for pkl_file in pkl_files[:min(10, len(pkl_files))]:  # Sample first 10 files
        filename = os.path.basename(pkl_file)
        timestamp = parse_timestamp_from_filename(filename)
        if timestamp:
            timestamps.append(timestamp)

    # If we have enough filename timestamps, calculate FPS
    if len(timestamps) >= 2:
        time_diffs = []
        for i in range(1, len(timestamps)):
            diff = (timestamps[i] - timestamps[i-1]).total_seconds()
            if diff > 0:  # Valid time difference
                time_diffs.append(diff)

        if time_diffs:
            avg_interval = sum(time_diffs) / len(time_diffs)
            fps = 1.0 / avg_interval
            print(f"Calculated FPS from filenames: {fps:.2f}")
            return fps

    # Method 2: Try to extract timestamps from file contents
    print("Trying to calculate FPS from file contents...")
    content_timestamps = []

    for pkl_file in pkl_files[:min(10, len(pkl_files))]:  # Sample first 10 files
        data = read_pkl(pkl_file)
        if data and isinstance(data, dict):
            # Look for common timestamp fields
            timestamp_fields = ['timestamp', 'time', 'step_time', 'obs_time', 'action_time']

            for field in timestamp_fields:
                if field in data:
                    try:
                        if isinstance(data[field], (int, float)):
                            content_timestamps.append(data[field])
                            break
                        elif isinstance(data[field], str):
                            # Try to parse string timestamp
                            try:
                                import dateutil.parser
                                parsed_time = dateutil.parser.parse(data[field])
                                content_timestamps.append(parsed_time.timestamp())
                                break
                            except ImportError:
                                # dateutil not available, try basic parsing
                                try:
                                    # Try ISO format
                                    parsed_time = datetime.datetime.fromisoformat(data[field].replace('Z', '+00:00'))
                                    content_timestamps.append(parsed_time.timestamp())
                                    break
                                except:
                                    continue
                    except:
                        continue

    # Calculate FPS from content timestamps
    if len(content_timestamps) >= 2:
        time_diffs = []
        for i in range(1, len(content_timestamps)):
            diff = content_timestamps[i] - content_timestamps[i-1]
            if diff > 0:  # Valid time difference
                time_diffs.append(diff)

        if time_diffs:
            avg_interval = sum(time_diffs) / len(time_diffs)
            fps = 1.0 / avg_interval
            print(f"Calculated FPS from file contents: {fps:.2f}")
            return fps

    print("Could not calculate FPS from timestamps")
    return None


def get_optimal_fps(pkl_files, default_fps=10):
    """
    Get optimal FPS for video generation

    Parameters:
        pkl_files (list): List of pkl file paths
        default_fps (int): Default FPS if calculation fails

    Returns:
        float: Optimal FPS value
    """
    calculated_fps = calculate_fps_from_timestamps(pkl_files)

    if calculated_fps:
        # Clamp FPS to reasonable range
        if calculated_fps < 1:
            fps = 1
            print(f"Calculated FPS too low ({calculated_fps:.2f}), using minimum: {fps}")
        elif calculated_fps > 60:
            fps = 60
            print(f"Calculated FPS too high ({calculated_fps:.2f}), using maximum: {fps}")
        else:
            fps = round(calculated_fps, 1)
            print(f"Using calculated FPS: {fps}")
        return fps
    else:
        print(f"Using default FPS: {default_fps}")
        return default_fps


def create_depth_colormap_standard(output_path="depth_color.png", depth_range=(0, 5000), colormap='jet'):
    """
    Create a standard depth colormap reference image

    Parameters:
        output_path (str): Path to save the colormap image
        depth_range (tuple): Min and max depth values in mm
        colormap (str): Colormap name ('jet', 'viridis', 'plasma', 'turbo', etc.)

    Returns:
        tuple: (colormap_function, depth_min, depth_max)
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.cm as cm
        from matplotlib.colors import Normalize

        depth_min, depth_max = depth_range

        # Create figure
        fig, ax = plt.subplots(figsize=(3, 8))

        # Create colormap
        cmap = cm.get_cmap(colormap)
        norm = Normalize(vmin=depth_min, vmax=depth_max)

        # Create colorbar
        sm = cm.ScalarMappable(cmap=cmap, norm=norm)
        sm.set_array([])

        # Add colorbar
        cbar = plt.colorbar(sm, ax=ax, orientation='vertical', fraction=1.0, aspect=20)
        cbar.set_label('Depth (mm)', rotation=270, labelpad=20, fontsize=12)

        # Set ticks and labels
        tick_values = np.linspace(depth_min, depth_max, 11)
        cbar.set_ticks(tick_values)
        cbar.set_ticklabels([f'{int(v)}' for v in tick_values])

        # Remove main axes
        ax.remove()

        # Set title
        plt.suptitle(f'Depth Colormap Standard\n({colormap})', fontsize=14, y=0.95)

        # Save image
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"Depth colormap standard saved: {output_path}")
        print(f"Depth range: {depth_min}-{depth_max} mm")
        print(f"Colormap: {colormap}")

        return cmap, depth_min, depth_max

    except ImportError:
        print("Warning: matplotlib not available, using OpenCV colormap")
        # Fallback to OpenCV colormap
        return cv2.COLORMAP_JET, depth_range[0], depth_range[1]


def apply_depth_colormap(depth_image, depth_range=None, colormap='jet'):
    """
    Apply colormap to depth image with adaptive or specified range

    Parameters:
        depth_image (numpy.ndarray): Grayscale depth image
        depth_range (tuple): Min and max depth values for normalization (None for auto)
        colormap (str): Colormap name

    Returns:
        numpy.ndarray: Colored depth image (BGR format for OpenCV)
    """
    # Ensure depth image is single channel
    if len(depth_image.shape) == 3:
        depth_image = cv2.cvtColor(depth_image, cv2.COLOR_BGR2GRAY)

    # Determine depth range
    if depth_range is None:
        # Auto-adaptive range based on current image
        valid_depths = depth_image[depth_image > 0]
        if len(valid_depths) > 0:
            depth_min = np.percentile(valid_depths, 1)
            depth_max = np.percentile(valid_depths, 99)
        else:
            depth_min, depth_max = 0, 255
    else:
        depth_min, depth_max = depth_range

    # Avoid division by zero
    if depth_max == depth_min:
        depth_max = depth_min + 1

    # Normalize depth to 0-255 range
    depth_normalized = np.clip(depth_image, depth_min, depth_max)
    depth_normalized = ((depth_normalized - depth_min) / (depth_max - depth_min) * 255).astype(np.uint8)

    # Apply colormap using OpenCV (more reliable and faster)
    colormap_cv = {
        'jet': cv2.COLORMAP_JET,
        'viridis': cv2.COLORMAP_VIRIDIS,
        'plasma': cv2.COLORMAP_PLASMA,
        'hot': cv2.COLORMAP_HOT,
        'cool': cv2.COLORMAP_COOL,
        'spring': cv2.COLORMAP_SPRING,
        'summer': cv2.COLORMAP_SUMMER,
        'autumn': cv2.COLORMAP_AUTUMN,
        'winter': cv2.COLORMAP_WINTER,
        'bone': cv2.COLORMAP_BONE,
        'turbo': cv2.COLORMAP_TURBO if hasattr(cv2, 'COLORMAP_TURBO') else cv2.COLORMAP_JET
    }

    cv_colormap = colormap_cv.get(colormap, cv2.COLORMAP_JET)
    colored = cv2.applyColorMap(depth_normalized, cv_colormap)

    return colored


def analyze_depth_range_per_camera(pkl_files, sample_size=10):
    """
    Analyze depth range for each camera separately to determine optimal colormap range

    Parameters:
        pkl_files (list): List of pkl file paths
        sample_size (int): Number of files to sample for analysis

    Returns:
        dict: Dictionary of camera_name -> (min_depth, max_depth, recommended_range)
    """
    print(f"Analyzing depth range per camera from {min(sample_size, len(pkl_files))} sample files...")

    camera_depth_values = {}

    for i, pkl_path in enumerate(pkl_files[:sample_size]):
        data = read_pkl(pkl_path)
        if data and 'depths' in data:
            for camera_name, depth_data in data['depths'].items():
                if camera_name not in camera_depth_values:
                    camera_depth_values[camera_name] = []

                decoded_depth = decode_image(depth_data)
                if decoded_depth is not None:
                    # Convert to grayscale if needed
                    if len(decoded_depth.shape) == 3:
                        depth_gray = cv2.cvtColor(decoded_depth, cv2.COLOR_BGR2GRAY)
                    else:
                        depth_gray = decoded_depth

                    # Collect non-zero depth values
                    valid_depths = depth_gray[depth_gray > 0]
                    if len(valid_depths) > 0:
                        camera_depth_values[camera_name].extend(valid_depths.flatten())

    camera_ranges = {}
    for camera_name, depth_values in camera_depth_values.items():
        if depth_values:
            depth_array = np.array(depth_values)
            min_depth = np.min(depth_array)
            max_depth = np.max(depth_array)

            # Calculate percentiles for better range
            p1 = np.percentile(depth_array, 1)
            p99 = np.percentile(depth_array, 99)

            # Recommend range based on percentiles
            recommended_range = (int(p1), int(p99))

            camera_ranges[camera_name] = {
                'min_depth': min_depth,
                'max_depth': max_depth,
                'recommended_range': recommended_range
            }

            print(f"  📷 {camera_name}:")
            print(f"    Absolute range: {min_depth:.0f} - {max_depth:.0f}")
            print(f"    Optimized range: {p1:.0f} - {p99:.0f}")
        else:
            print(f"  📷 {camera_name}: No valid depth data found")
            camera_ranges[camera_name] = {
                'min_depth': 0,
                'max_depth': 255,
                'recommended_range': (0, 255)
            }

    return camera_ranges


def create_image_grid(images_dict, grid_size=(2, 2), image_size=(640, 480)):
    """
    Create a grid layout from multiple camera images

    Parameters:
        images_dict (dict): Dictionary of camera_name -> image_array
        grid_size (tuple): Grid size as (cols, rows)
        image_size (tuple): Size to resize each image to (width, height)

    Returns:
        numpy.ndarray: Combined grid image
    """
    cols, rows = grid_size
    max_images = cols * rows

    # Resize all images and add titles
    processed_images = []
    for i, (camera_name, img) in enumerate(images_dict.items()):
        if i >= max_images:
            break

        # Resize image
        resized_img = cv2.resize(img, image_size)

        # Add title
        title_height = 30
        title_area = np.ones((title_height, image_size[0], 3), dtype=np.uint8) * 255
        cv2.putText(title_area, camera_name, (5, 20), cv2.FONT_HERSHEY_SIMPLEX,
                   0.5, (0, 0, 0), 1, cv2.LINE_AA)

        # Combine title and image
        combined = cv2.vconcat([title_area, resized_img])
        processed_images.append(combined)

    # Fill empty slots with blank images
    blank_img = np.ones((image_size[1] + 30, image_size[0], 3), dtype=np.uint8) * 255
    while len(processed_images) < max_images:
        processed_images.append(blank_img.copy())

    # Create grid
    grid_rows = []
    for row in range(rows):
        row_images = processed_images[row * cols:(row + 1) * cols]
        grid_rows.append(cv2.hconcat(row_images))

    return cv2.vconcat(grid_rows)


def create_videos_by_camera(input_dir, output_dir=".", fps=None, image_size=(640, 480)):
    """
    Create separate MP4 videos for each camera view from pkl files in a directory

    Parameters:
        input_dir (str): Directory containing pkl files
        output_dir (str): Directory to save output videos
        fps (float): Frames per second for the video (None for auto-calculation)
        image_size (tuple): Size for each camera image (width, height)

    Returns:
        dict: Dictionary of camera_name -> success_status
    """
    print(f"Scanning directory: {input_dir}")
    pkl_files = get_sorted_pkl_files(input_dir)

    if not pkl_files:
        print("No pkl files found in directory")
        return {}

    print(f"Found {len(pkl_files)} pkl files")

    # Auto-calculate FPS if not provided
    if fps is None:
        fps = get_optimal_fps(pkl_files, default_fps=10)
    else:
        print(f"Using provided FPS: {fps}")

    # Dictionary to store video writers for each camera
    video_writers = {}
    frame_counts = {}
    camera_names = set()

    # First pass: collect all camera names
    print("Detecting camera views...")
    for pkl_path in pkl_files[:10]:  # Check first 10 files to get camera names
        images = extract_images_from_pkl(pkl_path)
        if images:
            camera_names.update(images.keys())

    print(f"Found camera views: {list(camera_names)}")

    # Initialize frame counters
    for camera in camera_names:
        frame_counts[camera] = 0

    try:
        for i, pkl_path in enumerate(pkl_files):
            print(f"Processing {i+1}/{len(pkl_files)}: {os.path.basename(pkl_path)}")

            # Extract images from pkl
            images = extract_images_from_pkl(pkl_path)

            if not images:
                print(f"  No images found in {pkl_path}")
                continue

            # Process each camera view
            for camera_name, image in images.items():
                if camera_name not in camera_names:
                    continue

                # Resize image
                resized_image = cv2.resize(image, image_size)

                # Initialize video writer for this camera if not exists
                if camera_name not in video_writers:
                    output_video_path = os.path.join(output_dir, f"{camera_name}.mp4")
                    height, width = resized_image.shape[:2]
                    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                    video_writers[camera_name] = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
                    print(f"  Initialized video writer for {camera_name}: {width}x{height} at {fps} fps")

                # Write frame to video
                video_writers[camera_name].write(resized_image)
                frame_counts[camera_name] += 1

        # Report results
        results = {}
        for camera_name in camera_names:
            if camera_name in frame_counts and frame_counts[camera_name] > 0:
                print(f"Successfully created video for {camera_name} with {frame_counts[camera_name]} frames")
                results[camera_name] = True
            else:
                print(f"No frames found for camera {camera_name}")
                results[camera_name] = False

        return results

    except Exception as e:
        print(f"Error creating videos: {e}")
        return {camera: False for camera in camera_names}

    finally:
        # Release all video writers
        for camera_name, writer in video_writers.items():
            if writer is not None:
                writer.release()
                output_path = os.path.join(output_dir, f"{camera_name}.mp4")
                print(f"Video saved to: {output_path}")


def process_dataset_directory(dataset_dir, output_base_dir="./videos", fps=None, image_size=(640, 480)):
    """
    Process a dataset directory containing multiple sample folders
    Each sample folder contains pkl files for one episode

    Parameters:
        dataset_dir (str): Directory containing sample folders
        output_base_dir (str): Base directory to save output videos
        fps (float): Frames per second for the video (None for auto-calculation)
        image_size (tuple): Size for each camera image (width, height)

    Returns:
        dict: Dictionary of sample_name -> results
    """
    print(f"Processing dataset directory: {dataset_dir}")

    if not os.path.exists(dataset_dir):
        print(f"Error: Dataset directory does not exist: {dataset_dir}")
        return {}

    # Find all sample directories
    sample_dirs = []
    for item in os.listdir(dataset_dir):
        item_path = os.path.join(dataset_dir, item)
        if os.path.isdir(item_path):
            # Check if directory contains pkl files
            pkl_files = [f for f in os.listdir(item_path) if f.endswith('.pkl')]
            if pkl_files:
                sample_dirs.append((item, item_path))

    if not sample_dirs:
        print("No sample directories with pkl files found")
        return {}

    print(f"Found {len(sample_dirs)} sample directories:")
    for sample_name, _ in sample_dirs:
        print(f"  - {sample_name}")

    # Create base output directory
    if not os.path.exists(output_base_dir):
        os.makedirs(output_base_dir)
        print(f"Created output directory: {output_base_dir}")

    # Process each sample
    all_results = {}
    for i, (sample_name, sample_path) in enumerate(sample_dirs):
        print(f"\n{'='*60}")
        print(f"Processing sample {i+1}/{len(sample_dirs)}: {sample_name}")
        print(f"{'='*60}")

        # Create output directory for this sample
        sample_output_dir = os.path.join(output_base_dir, sample_name)
        if not os.path.exists(sample_output_dir):
            os.makedirs(sample_output_dir)

        # Process this sample
        results = create_videos_by_camera(
            sample_path,
            sample_output_dir,
            fps=fps,
            image_size=image_size
        )

        all_results[sample_name] = results

        # Report results for this sample
        successful_cameras = [cam for cam, success in results.items() if success]
        failed_cameras = [cam for cam, success in results.items() if not success]

        if successful_cameras:
            print(f"✓ Sample {sample_name}: Successfully created videos for {len(successful_cameras)} cameras")
        if failed_cameras:
            print(f"✗ Sample {sample_name}: Failed to create videos for {len(failed_cameras)} cameras")

    # Final summary
    print(f"\n{'='*60}")
    print("DATASET PROCESSING SUMMARY")
    print(f"{'='*60}")

    total_successful_samples = 0
    total_cameras_processed = 0

    for sample_name, results in all_results.items():
        successful_cameras = [cam for cam, success in results.items() if success]
        if successful_cameras:
            total_successful_samples += 1
            total_cameras_processed += len(successful_cameras)
            print(f"✓ {sample_name}: {len(successful_cameras)} cameras")
        else:
            print(f"✗ {sample_name}: No videos created")

    print(f"\nTotal: {total_successful_samples}/{len(sample_dirs)} samples processed successfully")
    print(f"Total: {total_cameras_processed} camera videos created")
    print(f"Output directory: {output_base_dir}")

    return all_results


def extract_images_and_depths_to_files(input_dir, output_base_dir=None, save_individual_images=True,
                                      depth_colormap='jet', adaptive_depth=True):
    """
    Extract images and depths from pkl files and save as individual image files
    Also create videos from the extracted images

    Parameters:
        input_dir (str): Directory containing pkl files
        output_base_dir (str): Base directory to save outputs (None for parent directory of input_dir)
        save_individual_images (bool): Whether to save individual image files
        depth_colormap (str): Colormap for depth visualization ('jet', 'viridis', 'plasma', etc.)
        adaptive_depth (bool): Whether to use adaptive depth range per camera/frame

    Returns:
        dict: Results of the extraction process
    """
    print(f"Extracting images and depths from: {input_dir}")
    pkl_files = get_sorted_pkl_files(input_dir)

    if not pkl_files:
        print("No pkl files found in directory")
        return {}

    print(f"Found {len(pkl_files)} pkl files")

    # Determine output directory
    if output_base_dir is None:
        # Use parent directory of input_dir
        output_base_dir = os.path.dirname(os.path.abspath(input_dir))
        print(f"Using parent directory as output: {output_base_dir}")
    else:
        print(f"Using specified output directory: {output_base_dir}")

    # Create output directories
    rgb_images_dir = os.path.join(output_base_dir, "rgb_images")
    depth_images_dir = os.path.join(output_base_dir, "depth_images")
    rgb_videos_dir = os.path.join(output_base_dir, "rgb_videos")
    depth_videos_dir = os.path.join(output_base_dir, "depth_videos")

    for dir_path in [rgb_images_dir, depth_images_dir, rgb_videos_dir, depth_videos_dir]:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"Created directory: {dir_path}")

    # Auto-calculate FPS
    fps = get_optimal_fps(pkl_files, default_fps=10)

    # Analyze depth range per camera if adaptive_depth is enabled
    camera_depth_ranges = {}
    if adaptive_depth:
        print("Using adaptive depth range for each camera (auto-normalize per frame)")
        # Will use per-frame normalization for optimal contrast
    else:
        print("Analyzing depth range per camera from sample files...")
        camera_depth_ranges = analyze_depth_range_per_camera(pkl_files, sample_size=min(10, len(pkl_files)))

    # Initialize video writers
    rgb_video_writers = {}
    depth_video_writers = {}
    camera_names = set()
    frame_count = 0

    try:
        for i, pkl_path in enumerate(pkl_files):
            print(f"Processing {i+1}/{len(pkl_files)}: {os.path.basename(pkl_path)}")

            # Extract images and depths from pkl
            data = read_pkl(pkl_path)
            if not data:
                print(f"  Failed to read {pkl_path}")
                continue

            # Get base filename without extension
            base_filename = os.path.splitext(os.path.basename(pkl_path))[0]

            # Process RGB images
            if 'images' in data and isinstance(data['images'], dict):
                for camera_name, image_data in data['images'].items():
                    camera_names.add(camera_name)

                    # Decode image
                    decoded_image = decode_image(image_data)
                    if decoded_image is not None:
                        # Save individual image file
                        if save_individual_images:
                            rgb_filename = f"{base_filename}_{camera_name}_rgb.png"
                            rgb_filepath = os.path.join(rgb_images_dir, rgb_filename)
                            cv2.imwrite(rgb_filepath, decoded_image)

                        # Initialize video writer if needed
                        if camera_name not in rgb_video_writers:
                            rgb_video_path = os.path.join(rgb_videos_dir, f"{camera_name}_rgb.mp4")
                            height, width = decoded_image.shape[:2]
                            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                            rgb_video_writers[camera_name] = cv2.VideoWriter(rgb_video_path, fourcc, fps, (width, height))
                            print(f"  Initialized RGB video writer for {camera_name}: {width}x{height} at {fps} fps")

                        # Write frame to video
                        rgb_video_writers[camera_name].write(decoded_image)

            # Process depth images
            if 'depths' in data and isinstance(data['depths'], dict):
                for camera_name, depth_data in data['depths'].items():
                    # Decode depth data
                    decoded_depth = decode_image(depth_data)
                    if decoded_depth is not None:
                        # Convert depth to grayscale if it's 3-channel
                        if len(decoded_depth.shape) == 3:
                            depth_gray = cv2.cvtColor(decoded_depth, cv2.COLOR_BGR2GRAY)
                        else:
                            depth_gray = decoded_depth

                        # Apply adaptive colormap to depth for better visualization
                        # Each camera/frame gets optimal contrast based on its own depth range
                        if adaptive_depth:
                            # Use adaptive range for optimal contrast per frame
                            depth_colored = apply_depth_colormap(depth_gray, None, depth_colormap)
                        else:
                            # Use pre-analyzed range for this camera
                            if camera_name in camera_depth_ranges:
                                camera_range = camera_depth_ranges[camera_name]['recommended_range']
                                depth_colored = apply_depth_colormap(depth_gray, camera_range, depth_colormap)
                            else:
                                # Fallback to adaptive if no pre-analyzed range
                                depth_colored = apply_depth_colormap(depth_gray, None, depth_colormap)

                        # Save individual depth image file (colored version)
                        if save_individual_images:
                            depth_filename = f"{base_filename}_{camera_name}_d.png"
                            depth_filepath = os.path.join(depth_images_dir, depth_filename)
                            cv2.imwrite(depth_filepath, depth_colored)

                        # Initialize depth video writer if needed
                        if camera_name not in depth_video_writers:
                            depth_video_path = os.path.join(depth_videos_dir, f"{camera_name}_d.mp4")
                            height, width = depth_colored.shape[:2]
                            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                            depth_video_writers[camera_name] = cv2.VideoWriter(depth_video_path, fourcc, fps, (width, height))
                            print(f"  Initialized depth video writer for {camera_name}: {width}x{height} at {fps} fps")

                        # Write colored depth frame to video
                        depth_video_writers[camera_name].write(depth_colored)

            frame_count += 1

        # Report results
        print(f"\n✅ Successfully processed {frame_count} frames")
        print(f"📷 RGB cameras: {len(rgb_video_writers)}")
        print(f"🔍 Depth cameras: {len(depth_video_writers)}")

        results = {
            'frames_processed': frame_count,
            'rgb_cameras': list(rgb_video_writers.keys()),
            'depth_cameras': list(depth_video_writers.keys()),
            'fps': fps
        }

        return results

    except Exception as e:
        print(f"Error during extraction: {e}")
        return {}

    finally:
        # Release all video writers
        for camera_name, writer in rgb_video_writers.items():
            if writer is not None:
                writer.release()
                rgb_video_path = os.path.join(rgb_videos_dir, f"{camera_name}_rgb.mp4")
                print(f"RGB video saved: {rgb_video_path}")

        for camera_name, writer in depth_video_writers.items():
            if writer is not None:
                writer.release()
                depth_video_path = os.path.join(depth_videos_dir, f"{camera_name}_d.mp4")
                print(f"Depth video saved: {depth_video_path}")


def process_dataset_images_and_depths(dataset_dir, output_base_dir=None, save_individual_images=True,
                                     depth_colormap='jet', adaptive_depth=True):
    """
    Process entire dataset to extract images and depths, create videos

    Parameters:
        dataset_dir (str): Directory containing sample folders
        output_base_dir (str): Base directory to save all outputs (None for parent directory)
        save_individual_images (bool): Whether to save individual image files
        depth_colormap (str): Colormap for depth visualization
        adaptive_depth (bool): Whether to use adaptive depth range per camera/frame

    Returns:
        dict: Dictionary of sample_name -> results
    """
    print(f"Processing dataset for image/depth extraction: {dataset_dir}")

    if not os.path.exists(dataset_dir):
        print(f"Error: Dataset directory does not exist: {dataset_dir}")
        return {}

    # Find all sample directories
    sample_dirs = []
    for item in os.listdir(dataset_dir):
        item_path = os.path.join(dataset_dir, item)
        if os.path.isdir(item_path):
            # Check if directory contains pkl files
            pkl_files = [f for f in os.listdir(item_path) if f.endswith('.pkl')]
            if pkl_files:
                sample_dirs.append((item, item_path))

    if not sample_dirs:
        print("No sample directories with pkl files found")
        return {}

    print(f"Found {len(sample_dirs)} sample directories:")
    for sample_name, _ in sample_dirs:
        print(f"  - {sample_name}")

    # Determine output directory
    if output_base_dir is None:
        # Use parent directory of dataset_dir
        output_base_dir = os.path.dirname(os.path.abspath(dataset_dir))
        print(f"Using parent directory as output base: {output_base_dir}")
    else:
        print(f"Using specified output base directory: {output_base_dir}")

    # Create base output directory
    if not os.path.exists(output_base_dir):
        os.makedirs(output_base_dir)
        print(f"Created output directory: {output_base_dir}")

    # Process each sample
    all_results = {}
    for i, (sample_name, sample_path) in enumerate(sample_dirs):
        print(f"\n{'='*60}")
        print(f"Processing sample {i+1}/{len(sample_dirs)}: {sample_name}")
        print(f"{'='*60}")

        # Create output directory for this sample
        sample_output_dir = os.path.join(output_base_dir, sample_name)
        if not os.path.exists(sample_output_dir):
            os.makedirs(sample_output_dir)

        # Extract images and depths for this sample
        results = extract_images_and_depths_to_files(
            sample_path,
            sample_output_dir,
            save_individual_images=save_individual_images,
            depth_colormap=depth_colormap,
            adaptive_depth=adaptive_depth
        )

        all_results[sample_name] = results

        # Report results for this sample
        if results:
            print(f"✓ Sample {sample_name}: Processed {results['frames_processed']} frames")
            print(f"  RGB cameras: {len(results['rgb_cameras'])}")
            print(f"  Depth cameras: {len(results['depth_cameras'])}")
            print(f"  FPS: {results['fps']}")
        else:
            print(f"✗ Sample {sample_name}: Processing failed")

    # Final summary
    print(f"\n{'='*60}")
    print("DATASET IMAGE/DEPTH EXTRACTION SUMMARY")
    print(f"{'='*60}")

    total_successful_samples = 0
    total_frames_processed = 0
    total_rgb_videos = 0
    total_depth_videos = 0

    for sample_name, results in all_results.items():
        if results and 'frames_processed' in results:
            total_successful_samples += 1
            total_frames_processed += results['frames_processed']
            total_rgb_videos += len(results['rgb_cameras'])
            total_depth_videos += len(results['depth_cameras'])
            print(f"✓ {sample_name}: {results['frames_processed']} frames, {len(results['rgb_cameras'])} RGB + {len(results['depth_cameras'])} depth videos")
        else:
            print(f"✗ {sample_name}: Processing failed")

    print(f"\nTotal: {total_successful_samples}/{len(sample_dirs)} samples processed successfully")
    print(f"Total: {total_frames_processed} frames processed")
    print(f"Total: {total_rgb_videos} RGB videos + {total_depth_videos} depth videos created")
    print(f"Output directory: {output_base_dir}")

    return all_results


def create_video_from_pkl_directory(input_dir, output_video_path, fps=None, grid_size=(2, 2), image_size=(640, 480)):
    """
    Create MP4 video from pkl files in a directory (legacy function for grid layout)

    Parameters:
        input_dir (str): Directory containing pkl files
        output_video_path (str): Output video file path
        fps (float): Frames per second for the video (None for auto-calculation)
        grid_size (tuple): Grid layout for multiple cameras (cols, rows)
        image_size (tuple): Size for each camera image (width, height)

    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Scanning directory: {input_dir}")
    pkl_files = get_sorted_pkl_files(input_dir)

    if not pkl_files:
        print("No pkl files found in directory")
        return False

    print(f"Found {len(pkl_files)} pkl files")

    # Auto-calculate FPS if not provided
    if fps is None:
        fps = get_optimal_fps(pkl_files, default_fps=10)
    else:
        print(f"Using provided FPS: {fps}")

    # Initialize video writer
    video_writer = None
    frame_count = 0

    try:
        for i, pkl_path in enumerate(pkl_files):
            print(f"Processing {i+1}/{len(pkl_files)}: {os.path.basename(pkl_path)}")

            # Extract images from pkl
            images = extract_images_from_pkl(pkl_path)

            if not images:
                print(f"  No images found in {pkl_path}")
                continue

            # Create grid image
            if len(images) == 1:
                # Single camera - use the image directly
                frame = list(images.values())[0]
                frame = cv2.resize(frame, (image_size[0] * grid_size[0], image_size[1] * grid_size[1]))
            else:
                # Multiple cameras - create grid
                frame = create_image_grid(images, grid_size, image_size)

            # Initialize video writer with first frame
            if video_writer is None:
                height, width = frame.shape[:2]
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                video_writer = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
                print(f"Initialized video writer: {width}x{height} at {fps} fps")

            # Write frame to video
            video_writer.write(frame)
            frame_count += 1

        print(f"Successfully created video with {frame_count} frames")
        return True

    except Exception as e:
        print(f"Error creating video: {e}")
        return False

    finally:
        if video_writer is not None:
            video_writer.release()
            print(f"Video saved to: {output_video_path}")

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single file: python pkl2.py <path_to_pkl_file>")
        print("  Single sample: python pkl2.py <sample_directory> [output_dir] [fps]")
        print("  Dataset processing: python pkl2.py <dataset_directory> --dataset [output_base_dir] [fps]")
        print("  Extract images/depths: python pkl2.py <sample_directory> --extract [output_dir|auto] [save_images]")
        print("  Extract dataset images/depths: python pkl2.py <dataset_directory> --extract-dataset [output_base_dir|auto] [save_images]")
        print("  Create grid video: python pkl2.py <directory_path> --grid [output_video.mp4] [fps] [grid_cols] [grid_rows]")
        print("Notes:")
        print("  - If fps is not specified or set to 'auto', it will be calculated from timestamps")
        print("  - FPS calculation uses filename timestamps or file content timestamps")
        print("  - save_images: 'true' or 'false' to save individual image files (default: true)")
        print("Examples:")
        print("  python pkl2.py /path/to/file.pkl")
        print("  python pkl2.py /path/to/sample_directory")
        print("  python pkl2.py /path/to/sample_directory ./videos auto")
        print("  python pkl2.py /path/to/dataset_directory --dataset ./all_videos auto")
        print("  python pkl2.py /path/to/sample_directory --extract auto true")
        print("  python pkl2.py /path/to/dataset_directory --extract-dataset auto true")
        print("  python pkl2.py /path/to/sample_directory --grid output.mp4 auto 2 2")
        return

    input_path = sys.argv[1]

    if not os.path.exists(input_path):
        print(f"Error: Path does not exist: {input_path}")
        return

    # Check if input is a file or directory
    if os.path.isfile(input_path):
        # Single file mode - show pkl content and extract images
        print(f"Processing single pkl file: {input_path}")

        data = read_pkl(input_path)
        if data is not None:
            print(f"Successfully loaded data of type: {type(data)}")

            # Extract and show images
            images = extract_images_from_pkl(input_path)
            if images:
                print(f"Found {len(images)} images:")
                for cam_name, img in images.items():
                    print(f"  {cam_name}: {img.shape}")

                # Save first image as preview
                if images:
                    first_cam = list(images.keys())[0]
                    preview_path = input_path.replace('.pkl', '_preview.jpg')
                    cv2.imwrite(preview_path, images[first_cam])
                    print(f"Saved preview image: {preview_path}")
            else:
                print("No images found in pkl file")
                print(f"Data preview: {str(data)[:200]}...")

    elif os.path.isdir(input_path):
        # Directory mode - check processing mode
        if len(sys.argv) > 2 and sys.argv[2] == "--dataset":
            # Dataset processing mode (multiple samples)
            print(f"Processing dataset directory: {input_path}")

            output_base_dir = sys.argv[3] if len(sys.argv) > 3 else "./all_videos"
            fps_arg = sys.argv[4] if len(sys.argv) > 4 else "auto"

            # Parse FPS argument
            if fps_arg == "auto":
                fps = None
                print("FPS: auto-calculate from timestamps")
            else:
                try:
                    fps = float(fps_arg)
                    print(f"FPS: {fps}")
                except ValueError:
                    fps = None
                    print(f"Invalid FPS '{fps_arg}', using auto-calculation")

            print(f"Output base directory: {output_base_dir}")

            # Process entire dataset
            all_results = process_dataset_directory(
                input_path,
                output_base_dir,
                fps=fps
            )

            if all_results:
                print("Dataset processing completed successfully!")
            else:
                print("Dataset processing failed!")

        elif len(sys.argv) > 2 and sys.argv[2] == "--extract":
            # Single sample image/depth extraction mode
            print(f"Extracting images and depths from sample: {input_path}")

            # If no output directory specified, use parent directory of input
            if len(sys.argv) > 3 and sys.argv[3] != "auto":
                output_dir = sys.argv[3]
            else:
                output_dir = None  # Will use parent directory

            save_images_arg = sys.argv[4] if len(sys.argv) > 4 else "true"
            save_images = save_images_arg.lower() in ['true', '1', 'yes']

            if output_dir:
                print(f"Output directory: {output_dir}")
            else:
                print(f"Output directory: parent directory of input")
            print(f"Save individual images: {save_images}")

            # Extract images and depths
            results = extract_images_and_depths_to_files(
                input_path,
                output_dir,
                save_individual_images=save_images
            )

            if results:
                print("Image/depth extraction completed successfully!")
                print(f"Processed {results['frames_processed']} frames")
                print(f"RGB videos: {len(results['rgb_cameras'])}")
                print(f"Depth videos: {len(results['depth_cameras'])}")
            else:
                print("Image/depth extraction failed!")

        elif len(sys.argv) > 2 and sys.argv[2] == "--extract-dataset":
            # Dataset image/depth extraction mode
            print(f"Extracting images and depths from dataset: {input_path}")

            # If no output directory specified, use parent directory of input
            if len(sys.argv) > 3 and sys.argv[3] != "auto":
                output_base_dir = sys.argv[3]
            else:
                output_base_dir = None  # Will use parent directory

            save_images_arg = sys.argv[4] if len(sys.argv) > 4 else "true"
            save_images = save_images_arg.lower() in ['true', '1', 'yes']

            if output_base_dir:
                print(f"Output base directory: {output_base_dir}")
            else:
                print(f"Output base directory: parent directory of input")
            print(f"Save individual images: {save_images}")

            # Process entire dataset
            all_results = process_dataset_images_and_depths(
                input_path,
                output_base_dir,
                save_individual_images=save_images
            )

            if all_results:
                print("Dataset image/depth extraction completed successfully!")
            else:
                print("Dataset image/depth extraction failed!")

        elif len(sys.argv) > 2 and sys.argv[2] == "--grid":
            # Grid video mode (legacy)
            print(f"Processing directory for grid video: {input_path}")

            output_video = sys.argv[3] if len(sys.argv) > 3 else "output_video.mp4"
            fps_arg = sys.argv[4] if len(sys.argv) > 4 else "auto"
            grid_cols = int(sys.argv[5]) if len(sys.argv) > 5 else 2
            grid_rows = int(sys.argv[6]) if len(sys.argv) > 6 else 2

            # Parse FPS argument
            if fps_arg == "auto":
                fps = None
                print("FPS: auto-calculate from timestamps")
            else:
                try:
                    fps = float(fps_arg)
                    print(f"FPS: {fps}")
                except ValueError:
                    fps = None
                    print(f"Invalid FPS '{fps_arg}', using auto-calculation")

            print(f"Output video: {output_video}")
            print(f"Grid size: {grid_cols}x{grid_rows}")

            # Create grid video
            success = create_video_from_pkl_directory(
                input_path,
                output_video,
                fps=fps,
                grid_size=(grid_cols, grid_rows)
            )

            if success:
                print("Grid video creation completed successfully!")
            else:
                print("Grid video creation failed!")

        else:
            # Single sample processing mode (default)
            print(f"Processing single sample directory: {input_path}")

            output_dir = sys.argv[2] if len(sys.argv) > 2 else "."
            fps_arg = sys.argv[3] if len(sys.argv) > 3 else "auto"

            # Parse FPS argument
            if fps_arg == "auto":
                fps = None
                print("FPS: auto-calculate from timestamps")
            else:
                try:
                    fps = float(fps_arg)
                    print(f"FPS: {fps}")
                except ValueError:
                    fps = None
                    print(f"Invalid FPS '{fps_arg}', using auto-calculation")

            # Create output directory if it doesn't exist
            if output_dir != "." and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"Created output directory: {output_dir}")

            print(f"Output directory: {output_dir}")

            # Create separate videos for each camera
            results = create_videos_by_camera(
                input_path,
                output_dir,
                fps=fps
            )

            # Report results
            successful_cameras = [cam for cam, success in results.items() if success]
            failed_cameras = [cam for cam, success in results.items() if not success]

            if successful_cameras:
                print(f"\n✓ Successfully created videos for: {', '.join(successful_cameras)}")
            if failed_cameras:
                print(f"\n✗ Failed to create videos for: {', '.join(failed_cameras)}")

            if successful_cameras:
                print("Separate camera videos creation completed successfully!")
            else:
                print("Video creation failed for all cameras!")


if __name__ == "__main__":
    main()


# Examples:
# Single file: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2/0606_114547/2025-06-06T11_46_15.813823.pkl
# Single sample: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2/0606_114547/ ./videos 15
# Dataset processing: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2/ --dataset ./all_videos 15
# Extract images/depths: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2/0606_114547/ --extract auto true
# Extract dataset: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2/ --extract-dataset auto true
# Grid video: python pkl2.py /home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2/0606_114547/ --grid output.mp4 15 2 2