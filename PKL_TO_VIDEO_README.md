# PKL 图像提取和视频生成工具

## 概述

修改后的 `pkl2.py` 脚本现在支持：
1. 从单个 pkl 文件中提取图像
2. 从目录中的多个 pkl 文件按时间顺序生成 MP4 视频

## 功能特性

- **时间戳解析**: 自动解析文件名中的时间戳（支持格式：`2025-06-09T16_51_05.803324.pkl`）
- **图像解码**: 支持多种图像格式（压缩图像、原始数据等）
- **多摄像头支持**: 自动检测并处理多个摄像头的图像
- **网格布局**: 将多个摄像头的图像排列成网格
- **错误处理**: 跳过损坏的 pkl 文件，继续处理有效文件

## 使用方法

### 1. 单个文件处理

```bash
python data_scape/pkl2.py <path_to_pkl_file>
```

示例：
```bash
python data_scape/pkl2.py /home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/2025-06-09T16_50_53.997363.pkl
```

功能：
- 显示 pkl 文件内容信息
- 提取并显示图像信息
- 保存第一个摄像头的预览图像

### 2. 目录视频生成

```bash
python data_scape/pkl2.py <directory_path> [output_video.mp4] [fps] [grid_cols] [grid_rows]
```

参数说明：
- `directory_path`: 包含 pkl 文件的目录路径
- `output_video.mp4`: 输出视频文件名（可选，默认：output_video.mp4）
- `fps`: 视频帧率（可选，默认：10）
- `grid_cols`: 网格列数（可选，默认：2）
- `grid_rows`: 网格行数（可选，默认：2）

示例：
```bash
# 使用默认参数
python data_scape/pkl2.py /path/to/pkl_directory

# 自定义参数
python data_scape/pkl2.py /path/to/pkl_directory my_video.mp4 15 2 2
```

## 输出示例

### 单文件处理输出：
```
Processing single pkl file: /path/to/file.pkl
Successfully loaded data of type: <class 'dict'>
Found 4 images:
  front: (480, 640, 3)
  left: (480, 640, 3)
  right: (480, 640, 3)
  wrist: (480, 640, 3)
Saved preview image: /path/to/file_preview.jpg
```

### 视频生成输出：
```
Processing directory: /path/to/pkl_directory
Output video: output.mp4
FPS: 10
Grid size: 2x2
Scanning directory: /path/to/pkl_directory
Found 431 pkl files
Processing 1/431: 2025-06-09T16_50_53.997363.pkl
Initialized video writer: 1280x1020 at 10 fps
...
Successfully created video with 105 frames
Video saved to: output.mp4
```

## 支持的图像格式

脚本自动检测和处理以下图像格式：
- 压缩图像（JPEG、PNG 等）
- 原始像素数据
- 常见分辨率：720x1280x3、480x640x3、480x640x4

## 错误处理

- 自动跳过损坏或无效的 pkl 文件
- 显示详细的错误信息
- 继续处理其他有效文件

## 依赖项

确保安装以下 Python 包：
```bash
pip install opencv-python numpy
```

## 注意事项

1. 文件名必须包含时间戳格式：`YYYY-MM-DDTHH_MM_SS.ffffff.pkl`
2. pkl 文件中的图像数据应存储在 `data['images']` 字典中
3. 生成的视频使用 MP4 格式，编码器为 'mp4v'
4. 网格大小会影响最终视频的分辨率

## 测试结果

在测试中，脚本成功处理了 431 个 pkl 文件中的 105 个有效文件，生成了 2.5MB 的 MP4 视频文件。
